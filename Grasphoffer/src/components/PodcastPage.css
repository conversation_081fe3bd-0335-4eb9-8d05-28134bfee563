.podcast-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
}

.podcast-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.back-to-dashboard {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.back-to-dashboard:hover {
  background: rgba(255, 255, 255, 0.3);
}

.podcast-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.podcast-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.4);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.retry-button {
  background: #FF5722;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 10px;
  font-weight: 600;
}

.retry-button:hover {
  background: #E64A19;
}

.topics-grid h2 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.topics-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.topic-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.topic-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.topic-header h3 {
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
  margin-right: 10px;
}

.topic-type {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.topic-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.topic-score {
  background: rgba(76, 175, 80, 0.3);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.podcast-generator {
  max-width: 800px;
  margin: 0 auto;
}

.selected-topic {
  text-align: center;
  margin-bottom: 40px;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 20px;
  font-weight: 600;
  transition: background 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.selected-topic h2 {
  font-size: 2rem;
  margin-bottom: 10px;
}

.topic-description {
  font-size: 1.1rem;
  opacity: 0.9;
}

.generate-section {
  text-align: center;
  margin: 40px 0;
}

.generate-button {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.generate-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.progress-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #81C784);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.progress-detail {
  font-size: 0.9rem;
  opacity: 0.8;
}

.podcast-player {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.podcast-info {
  text-align: center;
  margin-bottom: 30px;
}

.podcast-info h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.podcast-duration {
  font-size: 1rem;
  opacity: 0.8;
  margin-bottom: 15px;
}

.speakers-info {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.speaker {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
}

.audio-controls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.play-button, .stop-button {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.play-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.play-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.stop-button {
  background: linear-gradient(45deg, #f44336, #d32f2f);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.stop-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.key-takeaways {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  text-align: left;
}

.key-takeaways h4 {
  margin-bottom: 15px;
  color: #81C784;
  font-size: 1.2rem;
}

.key-takeaways ul {
  list-style: none;
  padding: 0;
}

.key-takeaways li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  line-height: 1.5;
}

.key-takeaways li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

@media (max-width: 768px) {
  .podcast-page {
    padding: 15px;
  }
  
  .podcast-header h1 {
    font-size: 2rem;
  }
  
  .topics-container {
    grid-template-columns: 1fr;
  }
  
  .speakers-info {
    gap: 15px;
  }
  
  .audio-controls {
    flex-direction: column;
    align-items: center;
  }
}

.progress-time-estimate {
  font-size: 0.9rem;
  color: #81C784;
  margin: 5px 0;
  font-weight: 500;
}

.progress-tips {
  margin-top: 15px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4CAF50;
}

.progress-tips p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.audio-save-toggle {
  margin-bottom: 20px;
  text-align: center;
}

.toggle-container {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.toggle-container input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 50px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  position: relative;
  transition: background 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider {
  background: #4CAF50;
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(26px);
}

.toggle-label {
  user-select: none;
}