/* File Processor Component Styles */
.file-processor {
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Processing Status */
.processing-status {
  text-align: center;
}

.processing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.processing-header h4 {
  color: #4CAF50;
  margin: 0;
  font-size: 1.2rem;
}

.file-count {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.current-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.file-icon {
  font-size: 20px;
}

.file-name {
  color: #ffffff;
  font-weight: 500;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: 6px;
  transition: width 0.3s ease;
  min-width: 2px; /* Ensure it's visible even at low percentages */
}

.progress-text {
  color: #4CAF50;
  font-weight: 600;
  font-size: 14px;
  min-width: 40px;
}

.processing-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
}

.processing-info p {
  margin: 5px 0;
}

/* Results */
.processing-results {
  color: #ffffff;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-header h4 {
  color: #4CAF50;
  margin: 0;
  font-size: 1.2rem;
}

.results-summary {
  display: flex;
  gap: 15px;
}

.success-count {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
}

.error-count {
  color: #ff6b6b;
  font-size: 14px;
  font-weight: 500;
}

/* Success Results */
.success-results {
  margin-bottom: 20px;
}

.success-results h5 {
  color: #4CAF50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-item {
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-item.success {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.result-item.error {
  background: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.3);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.result-icon {
  font-size: 16px;
}

.result-filename {
  font-weight: 600;
  color: #ffffff;
  flex: 1;
}

.result-method {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.result-details {
  margin-top: 10px;
}

.text-preview {
  margin-bottom: 10px;
}

.text-preview strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.text-preview p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
  background: rgba(0, 0, 0, 0.2);
  padding: 8px;
  border-radius: 4px;
}

.storage-info {
  margin-top: 10px;
}

.storage-info strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.storage-link {
  color: #4CAF50;
  text-decoration: none;
  font-size: 13px;
  padding: 4px 8px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  transition: all 0.2s ease;
}

.storage-link:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
}

/* Error Results */
.error-results {
  margin-bottom: 20px;
}

.error-results h5 {
  color: #ff6b6b;
  margin-bottom: 15px;
  font-size: 1rem;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.error-message {
  color: #ff6b6b;
  font-size: 14px;
  flex: 1;
}

/* File Queue */
.file-queue {
  color: #ffffff;
}

.file-queue h4 {
  color: #4CAF50;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-info .file-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
}

.file-details {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

/* Buttons */
.process-btn,
.clear-results-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.process-btn:hover:not(:disabled),
.clear-results-btn:hover {
  background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.process-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.clear-results-btn {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}

.clear-results-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: none;
  box-shadow: none;
}

.results-actions {
  text-align: center;
  margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .file-processor {
    padding: 15px;
  }

  .processing-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .results-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .results-summary {
    justify-content: center;
  }

  .result-header {
    flex-wrap: wrap;
  }

  .current-file {
    flex-direction: column;
    text-align: center;
  }

  .file-name {
    max-width: 250px;
  }
}
