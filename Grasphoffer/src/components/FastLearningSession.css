.fast-learning-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  padding: 20px;
  position: relative;
  font-family: 'Inter', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
}

.nav-menu {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(26, 26, 46, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-menu:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: scale(1.1);
}

.title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 60px 0 20px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.topic-display {
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  font-weight: 500;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 30px;
}

.loading-section p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 500;
}

.progress-section {
  max-width: 600px;
  margin: 0 auto 40px auto;
  z-index: 10;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
  white-space: nowrap;
  min-width: 200px;
  margin: 0 auto;
}

.flashcard-section {
  max-width: 700px;
  margin: 0 auto;
  padding: 0 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 500px;
}

.flashcard-wrapper {
  perspective: 1000px;
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
}

.flashcard {
  width: 100%;
  max-width: 600px;
  height: 350px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.flashcard.flipped {
  transform: rotateY(180deg);
}

.flashcard-front,
.flashcard-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  color: #ffffff;
  border-radius: 20px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.flashcard-back {
  transform: rotateY(180deg);
}

.card-number {
  font-size: 0.9rem;
  color: #4CAF50;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.card-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.4;
}

.tap-instruction {
  margin-top: 20px;
  opacity: 0.6;
  font-size: 0.9rem;
}

.answer-content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e0e0e0;
}

.card-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  margin: 40px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Card Indicators */
.card-indicators {
  display: flex;
  gap: 8px;
  align-items: center;
}

.indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px solid transparent;
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.indicator.active {
  background: #4CAF50;
  border-color: #66BB6A;
  transform: scale(1.2);
}

.indicator.studied {
  background: #66BB6A;
  border-color: #4CAF50;
}

.indicator.studied.active {
  background: #4CAF50;
  border-color: #2E7D32;
}

.tick-mark {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.nav-button {
  flex: 1;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  border-color: rgba(255, 255, 255, 0.1);
}

.completion-actions {
  text-align: center;
}

/* Genie Explain Button */
.genie-explain-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
}

.genie-explain-button:hover {
  background: linear-gradient(135deg, #F7931E 0%, #FF6B35 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.genie-explain-button .genie-icon {
  font-size: 16px;
}

.start-evaluation-button {
  padding: 16px 32px;
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.start-evaluation-button:hover {
  background: linear-gradient(135deg, #388E3C, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

/* Evaluation Styles */
.evaluation-progress {
  max-width: 600px;
  margin: 0 auto 40px auto;
}

.question-section {
  max-width: 700px;
  margin: 0 auto;
}

.question-card {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  color: #ffffff;
  border-radius: 20px;
  padding: 30px 25px;
  margin-bottom: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.question-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 25px;
  line-height: 1.4;
  color: #ffffff;
  text-align: center;
}

.options-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.option-button {
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: #ffffff;
  font-weight: 500;
  user-select: none;
  display: flex;
  align-items: center;
}

.option-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.option-button.selected {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  color: #ffffff;
}

.next-question-button {
  width: 100%;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.next-question-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.next-question-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Completion Styles */
.completion-section {
  max-width: 500px;
  margin: 0 auto;
  text-align: center;
}

.completion-icon {
  font-size: 4rem;
  margin-bottom: 30px;
}

.results-summary h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 30px;
}

.score-display {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.score-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.score-details {
  font-size: 1.1rem;
  opacity: 0.9;
}

.result-status {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
}

.result-status.passed {
  background: rgba(16, 185, 129, 0.2);
  border: 2px solid rgba(16, 185, 129, 0.5);
}

.result-status.needs-improvement {
  background: rgba(245, 158, 11, 0.2);
  border: 2px solid rgba(245, 158, 11, 0.5);
}

.restart-button {
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.restart-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .fast-learning-container {
    padding: 15px;
  }

  .flashcard-section {
    padding: 0 10px;
    min-height: 400px;
  }

  .flashcard {
    height: 300px;
  }

  .card-navigation {
    gap: 15px;
  }

  .genie-explain-button {
    bottom: 15px;
    right: 15px;
    padding: 10px 16px;
    font-size: 13px;
  }

  .genie-explain-button .genie-icon {
    font-size: 14px;
  }

  .nav-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .card-indicators {
    gap: 6px;
  }

  .indicator {
    width: 20px;
    height: 20px;
  }

  .tick-mark {
    font-size: 12px;
  }

  .title {
    font-size: 2rem;
    margin: 40px 0 20px 0;
  }

  .topic-display {
    font-size: 1rem;
    margin-bottom: 30px;
  }
}
