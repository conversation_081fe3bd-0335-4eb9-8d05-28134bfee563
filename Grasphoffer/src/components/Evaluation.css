/* Evaluation Component Styles - Dashboard Theme */
.evaluation-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header Navigation */
.evaluation-container::before {
  content: '';
  position: fixed;
  top: 20px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  z-index: 1000;
}

.evaluation-container::after {
  content: '';
  position: fixed;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  z-index: 1000;
}

/* Navigation Icons */
.nav-menu {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
}

.nav-profile {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
}

/* Title */
.title {
  font-family: 'Brush Script MT', cursive;
  font-size: 3rem;
  font-weight: 300;
  margin: 80px 0 60px 0;
  color: #ffffff;
  text-align: center;
}

/* Quiz Section */
.quiz-section {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.quiz-header {
  background: #4CAF50;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 30px;
}

.progress {
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* Question Card */
.question-card {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px 25px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.question-card h3 {
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 20px;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.option {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 12px 20px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
  display: flex;
  align-items: center;
}

.option:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.option.selected {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
}

.option-letter {
  font-weight: bold;
  margin-right: 10px;
}

.question-actions {
  margin-top: 20px;
}

.next-button {
  padding: 12px 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
}

.next-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.next-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Results Section */
.results-section {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.results-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
}

.result-card {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 20px;
  color: #ffffff;
  width: 200px;
  text-align: center;
}

.passed {
  border-color: #4CAF50;
}

.failed {
  border-color: #ff3b3b;
}

.results-actions {
  margin-top: 20px;
}

.action-button {
  padding: 12px 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Report Section */
.report-section {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.report-content {
  text-align: left;
  margin-top: 20px;
}

.general-remark {
  margin-bottom: 15px;
}

.general-remark h3 {
  margin-bottom: 5px;
}

.recommendations {
  margin-top: 15px;
}

.report-actions {
  margin-top: 30px;
}

.action-button.primary {
  background: #4CAF50;
  border-color: #4CAF50;
  color: #ffffff;
}

.action-button.primary:hover {
  background: #45a349;
  border-color: #45a349;
}

/* Responsive Design */
@media (max-width: 768px) {
  .evaluation-container {
    padding: 20px 15px;
  }
  
  .title {
    font-size: 2.5rem;
    margin: 60px 0 40px 0;
  }
  
  .quiz-section {
    max-width: 100%;
  }
  
  .question-card {
    padding: 20px 15px;
  }
  
  .question-card h3 {
    font-size: 16px;
  }
  
  .option {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .next-button {
    width: 100%;
    padding: 14px 20px;
  }
  
  .results-grid {
    flex-direction: column;
    align-items: center;
  }
  
  .result-card {
    width: 100%;
    max-width: 300px;
  }
  
  .report-content {
    padding: 0 10px;
  }
  
  .action-button {
    width: 100%;
    padding: 14px 20px;
  }
}
