/* TheHopper Chatbot Styles */
.thehopper-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.thehopper-container {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  width: 90%;
  max-width: 800px;
  height: 80vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  border: 2px solid #4CAF50;
  position: relative;
  overflow: hidden;
}

.thehopper-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39, #4CAF50);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.thehopper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: rgba(76, 175, 80, 0.1);
  border-bottom: 1px solid rgba(76, 175, 80, 0.3);
}

.thehopper-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.close-button {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 28px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.thehopper-status {
  padding: 15px 25px;
  border-bottom: 1px solid rgba(76, 175, 80, 0.2);
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #FFD700;
  font-size: 14px;
}

.status-ready {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-top: 2px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress Bar Styles */
.progress-container {
  padding: 15px 25px;
  border-bottom: 1px solid rgba(76, 175, 80, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer-progress 2s ease-in-out infinite;
}

@keyframes shimmer-progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  color: #4CAF50;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 25px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.5);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.7);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  align-self: flex-end;
}

.message.hopper {
  align-self: flex-start;
}

.message.system {
  align-self: center;
  max-width: 90%;
}

.message-content {
  padding: 15px 20px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
  text-align: left;
}

.message.user .message-content {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
  border-bottom-right-radius: 5px;
}

.message.hopper .message-content {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-bottom-left-radius: 5px;
}

.message.system .message-content {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.4);
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}

.message-timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 5px;
  align-self: flex-end;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message.user .message-timestamp {
  align-self: flex-end;
}

.message.hopper .message-timestamp {
  align-self: flex-start;
}

.message.system .message-timestamp {
  align-self: center;
}

.processing-time {
  color: #FFC107;
  font-weight: 500;
}

.typing-indicator {
  display: inline-flex;
  gap: 4px;
  margin-right: 10px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: #4CAF50;
  border-radius: 50%;
  animation: typing 1.4s ease-in-out infinite both;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-container {
  padding: 20px 25px;
  border-top: 1px solid rgba(76, 175, 80, 0.2);
  display: flex;
  gap: 15px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
  font-family: inherit;
}

.input-container textarea:focus {
  border-color: #4CAF50;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.input-container textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-container textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 80px;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.thehopper-footer {
  padding: 15px 25px;
  text-align: center;
  border-top: 1px solid rgba(76, 175, 80, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.thehopper-footer small {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  line-height: 1.4;
}

.processing-note {
  display: block;
  margin-top: 8px;
  color: #FFC107;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .thehopper-container {
    width: 95%;
    height: 90vh;
    border-radius: 15px;
  }
  
  .thehopper-header {
    padding: 15px 20px;
  }
  
  .thehopper-header h2 {
    font-size: 20px;
  }
  
  .messages-container {
    padding: 15px 20px;
  }
  
  .input-container {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
  }
  
  .send-button {
    width: 100%;
    padding: 14px;
  }
  
  .message {
    max-width: 90%;
  }
  
  .progress-container {
    padding: 10px 20px;
  }
}

/* Dark theme enhancements */
.thehopper-container {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8), 
              0 0 0 1px rgba(76, 175, 80, 0.1);
}

/* Message content styling for markdown-like content */
.message.hopper .message-content {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.message.hopper .message-content strong {
  color: #FFD700;
  font-weight: 600;
}

.message.hopper .message-content em {
  color: #81C784;
  font-style: italic;
}

.message.hopper .message-content code {
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #FFD700;
}

/* Enhanced progress animations */
.progress-fill {
  background: linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39);
  background-size: 200% 100%;
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Enhanced Markdown styling for TheHopper messages */
.message.hopper .message-content h1,
.message.hopper .message-content h2,
.message.hopper .message-content h3,
.message.hopper .message-content h4,
.message.hopper .message-content h5,
.message.hopper .message-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.3;
  color: #81C784;
}

.message.hopper .message-content h1 { font-size: 18px; }
.message.hopper .message-content h2 { font-size: 16px; }
.message.hopper .message-content h3 { font-size: 15px; }
.message.hopper .message-content h4 { font-size: 14px; }

.message.hopper .message-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.message.hopper .message-content ul,
.message.hopper .message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message.hopper .message-content li {
  margin: 4px 0;
  line-height: 1.5;
}

.message.hopper .message-content pre {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  color: #E8F5E8;
}

.message.hopper .message-content blockquote {
  border-left: 3px solid #4CAF50;
  padding-left: 12px;
  margin: 8px 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
}

.message.hopper .message-content a {
  color: #81C784;
  text-decoration: none;
}

.message.hopper .message-content a:hover {
  text-decoration: underline;
  color: #A5D6A7;
}

/* Error message markdown styling */
.message.error .message-content {
  background: rgba(244, 67, 54, 0.2);
  color: #FF5722;
  border: 1px solid rgba(244, 67, 54, 0.4);
  border-radius: 12px;
}

.message.error .message-content h1,
.message.error .message-content h2,
.message.error .message-content h3,
.message.error .message-content h4 {
  color: #FF8A65;
  margin: 12px 0 8px 0;
}

.message.error .message-content strong {
  color: #FF5722;
  font-weight: 600;
}

.message.error .message-content code {
  background: rgba(0, 0, 0, 0.3);
  color: #FFB74D;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}