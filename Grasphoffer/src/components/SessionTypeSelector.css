/* Modern Session Type Selector */
.session-type-selector {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.selector-header {
  text-align: center;
  margin-bottom: 40px;
}

.selector-header h3 {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.selector-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  margin: 0;
  line-height: 1.5;
}

.session-types-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  margin-bottom: 40px;
}

@media (min-width: 768px) {
  .session-types-grid {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
  }
}

.session-type-card {
  background: rgba(26, 26, 46, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.session-type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.session-type-card:hover:not(.disabled) {
  border-color: #667eea;
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
  transform: translateY(-4px);
  background: rgba(26, 26, 46, 0.8);
}

.session-type-card:hover:not(.disabled)::before {
  transform: scaleX(1);
}

.session-type-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.15);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
  transform: translateY(-4px);
}

.session-type-card.selected::before {
  transform: scaleX(1);
}

.session-type-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.session-type-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 28px;
}

.session-type-icon {
  font-size: 48px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 8px rgba(102, 126, 234, 0.3));
}

.session-type-info {
  flex: 1;
}

.session-type-info h4 {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 12px 0;
}

.session-type-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  margin: 0;
  line-height: 1.6;
}

.selection-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  flex-shrink: 0;
}

.session-type-card.selected .selection-indicator {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.check-mark {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.session-type-features {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.session-type-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 16px;
}

.session-type-features li {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 10px 0;
}

.session-type-features li::before {
  content: '✓';
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.selection-summary {
  text-align: center;
  margin-top: 32px;
}

.selected-type-summary {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: rgba(102, 126, 234, 0.15);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 50px;
  color: #ffffff;
  font-size: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.summary-icon {
  font-size: 24px;
}

.summary-text strong {
  color: #667eea;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .session-types-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .session-type-card {
    padding: 28px;
    min-height: 280px;
  }

  .session-type-header {
    gap: 18px;
    margin-bottom: 24px;
  }

  .session-type-icon {
    font-size: 36px;
  }

  .session-type-info h4 {
    font-size: 22px;
  }

  .session-type-info p {
    font-size: 16px;
  }

  .session-type-features li {
    font-size: 15px;
    gap: 12px;
    padding: 8px 0;
  }
}
