{"name": "grasphoffer_warp", "version": "0.1.0", "private": true, "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.53.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "dotenv": "^17.2.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --transformIgnorePatterns \"node_modules/(?!react-markdown|@google/generative-ai)/\"", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(@google/generative-ai|react-markdown)/)"], "testEnvironment": "jsdom", "setupFilesAfterEnv": ["@testing-library/jest-dom"]}}