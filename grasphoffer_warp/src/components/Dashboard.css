/* Dashboard Component Styles */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(45, 45, 45, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-title {
  font-family: "Leckerli One";
  font-size: 2rem;
  font-weight: 300;
  margin: 0;
  color: #4CAF50;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-email {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.signout-button {
  padding: 8px 16px;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.signout-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #4CAF50;
}

.dashboard-main {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 50px;
}

.welcome-section h2 {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 15px;
  color: #ffffff;
}

.welcome-section p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.topic-input-section {
  margin-bottom: 60px;
}

.input-card {
  background: rgba(45, 45, 45, 0.8);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.input-card h3 {
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: 30px;
  color: #ffffff;
}

.topic-input-group {
  display: flex;
  gap: 15px;
  max-width: 600px;
  margin: 0 auto;
}

.topic-input {
  flex: 1;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.topic-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.topic-input:focus {
  outline: none;
  border-color: #4CAF50;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.start-learning-button {
  padding: 16px 30px;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.start-learning-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.start-learning-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.recent-sessions {
  margin-top: 60px;
}

.recent-sessions h3 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 30px;
  color: #ffffff;
}

.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.session-card {
  background: rgba(45, 45, 45, 0.6);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.session-card:hover {
  background: rgba(45, 45, 45, 0.8);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.session-card h4 {
  color: #4CAF50;
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.session-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .dashboard-main {
    padding: 20px;
  }
  
  .welcome-section h2 {
    font-size: 2rem;
  }
  
  .input-card {
    padding: 25px 20px;
  }
  
  .topic-input-group {
    flex-direction: column;
    gap: 15px;
  }
  
  .topic-input, .start-learning-button {
    width: 100%;
  }
  
  .sessions-grid {
    grid-template-columns: 1fr;
  }
}
